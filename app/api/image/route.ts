import { Experimental_GeneratedImage as GeneratedImage } from 'ai';
import { after, NextResponse } from 'next/server';
import * as ChatHistoryDB from '@/service/db/chat-history';
import * as ChatMessageDB from '@/service/db/chat-message';
import * as MediaStorageDB from '@/service/db/media-storage';
import { checkUserModelAccess, updateUserUsage } from '@/service/managers/subscription-manager';
import { calculateImageCreditCost } from '@/service/managers/credit-cost-manager';
import { createSupabaseServerClient } from '@/utils/supabase/server';
import {
	getImageModelEndpoint,
	ImageModelDisplayName,
	imageModelDisplayNameToProviderMap,
	ImageModelProvider,
	ImageModelsList,
} from '@/models/image/image-generation-models';
import {
	generateImageEditResponseFromOpenAI,
	generateImageResponseFromOpenAI,
} from '@/models/providers/openai';
import { generateImageResponseFromVertexAI } from '@/models/providers/vertex-ai';
import { generateImageResponseFromFalAI } from '@/models/providers/fal-ai';
import { Attachment, ChatMessage } from '@/types/chat';
import { createImageErrorResponse } from '@/utils/error-handling';

export async function POST(req: Request) {
	const supabase = await createSupabaseServerClient();
	const {
		data: { user },
		error: authError,
	} = await supabase.auth.getUser();

	if (authError || !user) {
		return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
	}

	const userId = user.id;

	const formData = await req.formData();
	const chatId = formData.get('chatId') as string;
	const prompt = formData.get('prompt') as string;
	const imageModel = formData.get('model') as ImageModelDisplayName;
	const isNewChat = formData.get('isNewChat') === 'true';
	const isSharedChat = formData.get('isSharedChat') === 'true';
	const sharedMessages = JSON.parse(
		(formData.get('sharedMessages') as string) || '[]'
	) as ChatMessage[];
	const isImageEdit = formData.get('isImageEdit') === 'true';
	const requestType =
		(formData.get('requestType') as 'submit' | 'edit' | 'regenerate') || 'submit';
	const messageId = formData.get('messageId') as string;
	const params = JSON.parse((formData.get('params') as string) || '{}');

	// Validate required fields
	if (!chatId || !userId || !prompt || !imageModel) {
		console.error('Missing required fields');
		return NextResponse.json({ error: 'Bad Request' }, { status: 400 });
	}

	// Validate messageId for edit and regenerate
	if ((requestType === 'edit' || requestType === 'regenerate') && !messageId) {
		console.error(`Message ID is required for ${requestType}`);
		return NextResponse.json({ error: 'Bad Request' }, { status: 400 });
	}

	// Get model tier and check access
	const modelInfo = ImageModelsList.find(
		(m: { key: string; tier?: number }) => m.key === imageModel
	);
	if (!modelInfo?.tier) {
		return NextResponse.json({ error: 'Invalid model' }, { status: 400 });
	}

	const accessCheck = await checkUserModelAccess(supabase, userId, modelInfo.tier, 'credit');
	if (!accessCheck.hasAccess) {
		return NextResponse.json({ error: accessCheck.error }, { status: 403 });
	}

	let uploadedAttachments: Attachment[] = [];
	let uploadedImageFiles: File[] = [];
	let uploadedImageUrls: string[] = [];
	if (requestType === 'submit') {
		try {
			if (isImageEdit) {
				uploadedAttachments = formData
					.getAll('images')
					.map((file) => JSON.parse(file as string) as Attachment);
				const imageUrlResults = await Promise.all(
					uploadedAttachments.map((attachment) =>
						MediaStorageDB.storeBase64Image(supabase, attachment.url, userId)
					)
				);
				uploadedImageUrls = imageUrlResults
					.map((result) => result.data)
					.filter(Boolean) as string[];
				uploadedAttachments = uploadedAttachments.map((attachment, index) => ({
					...attachment,
					url: uploadedImageUrls[index],
				}));
			} else {
				uploadedImageFiles = formData.getAll('images').map((file) => file as File);
				const imageUrlResults = await Promise.all(
					uploadedImageFiles.map((file) =>
						MediaStorageDB.storeUploadedImage(supabase, file, userId)
					)
				);
				uploadedImageUrls = imageUrlResults
					.map((result) => result.data)
					.filter(Boolean) as string[];
				uploadedAttachments = uploadedImageFiles.map((file, index) => ({
					type: file.type,
					name: file.name,
					url: uploadedImageUrls[index],
				}));
			}
		} catch (error) {
			console.error('Error storing uploaded image:', error);
			return NextResponse.json(
				{ error: 'Failed to process uploaded image' },
				{ status: 500 }
			);
		}
	} else {
		formData.getAll('images').forEach((file) => {
			const attachment = JSON.parse(file as string) as Attachment;
			uploadedImageUrls.push(attachment.url);
			uploadedAttachments.push(attachment);
		});
	}

	const modelProvider = imageModelDisplayNameToProviderMap[imageModel];

	// Currently, a single image upload is supported.
	const uploadedImageFile = uploadedImageFiles.length > 0 ? uploadedImageFiles[0] : null;
	const uploadedImageUrl = uploadedImageUrls.length > 0 ? uploadedImageUrls[0] : undefined;

	// TODO: Determine the endpoint and save it correctly.

	let generatedImages: GeneratedImage[];
	try {
		switch (modelProvider) {
			case ImageModelProvider.BlackForestLabs:
			case ImageModelProvider.Ideogram:
			case ImageModelProvider.StabilityAI:
				generatedImages = await generateImageResponseFromFalAI(
					imageModel,
					prompt,
					params,
					uploadedImageUrl,
					req.signal
				);
				break;
			case ImageModelProvider.Google:
				generatedImages = await generateImageResponseFromVertexAI(
					imageModel,
					prompt,
					params,
					req.signal
				);
				break;
			case ImageModelProvider.OpenAI:
				if (uploadedImageUrl) {
					generatedImages = await generateImageEditResponseFromOpenAI(
						prompt,
						uploadedImageUrl,
						uploadedImageFile,
						params,
						req.signal
					);
				} else {
					generatedImages = await generateImageResponseFromOpenAI(
						imageModel,
						prompt,
						params,
						req.signal
					);
				}
				break;
			default:
				return NextResponse.json(
					{ error: 'Unsupported image model provider' },
					{ status: 400 }
				);
		}
	} catch (error: any) {
		console.error(`Error generating image with ${modelProvider}:`, error);
		return NextResponse.json(
			{
				error: createImageErrorResponse(error, modelProvider, imageModel),
			},
			{ status: 500 }
		);
	}

	if (generatedImages.length === 0) {
		return NextResponse.json(
			{
				error: JSON.stringify({
					message:
						'The model returned no images. Please try again or with a different prompt.',
					errorDetails: 'No images generated',
				}),
			},
			{ status: 500 }
		);
	}

	const generatedImageResults: Attachment[] = generatedImages.map((image: GeneratedImage) => ({
		type: image.mimeType,
		url: `data:${image.mimeType};base64,${image.base64}`,
	}));

	after(async () => {
		// Calculate dynamic credit cost based on model and parameters
		const creditCost = calculateImageCreditCost(imageModel as ImageModelDisplayName, {
			n: (params?.n as number) || 1,
			resolution: params?.size || '1024x1024',
		});
		const result = await updateUserUsage(supabase, userId, 'credits', creditCost);

		if (!result.success) {
			console.error('Failed to update image usage:', result.error);
		}

		const generatedImageAttachments: Attachment[] = [];
		for (const image of generatedImages) {
			const { data: imageUrl, error: storeError } = await MediaStorageDB.storeGeneratedImage(
				supabase,
				image,
				userId
			);
			if (storeError || !imageUrl) {
				console.error('Failed to store generated image:', storeError);
				continue;
			}
			generatedImageAttachments.push({
				type: image.mimeType,
				url: imageUrl,
			});
		}

		switch (requestType) {
			case 'submit':
				if (isNewChat || isSharedChat) {
					try {
						const { error } = await ChatHistoryDB.createChatHistory(
							supabase,
							userId,
							chatId,
							prompt,
							'image'
						);
						if (error) {
							console.error('Failed to create chat history:', error);
							if (error.code !== '23505') {
								throw new Error('Failed to create chat history');
							}
						}
					} catch (error) {
						console.error('Failed to create chat history:', error);
					}
				}

				if (isSharedChat) {
					try {
						const { error } = await ChatMessageDB.createMultipleChatMessages(
							supabase,
							chatId,
							sharedMessages
						);
						if (error) {
							console.error('Failed to save shared chat messages:', error);
							throw new Error('Failed to save shared chat messages');
						}
					} catch (error) {
						console.error('Failed to save shared chat messages:', error);
					}
				}

				const newMessage: ChatMessage = {
					chatId,
					id: messageId,
					prompt: {
						text: prompt,
						attachments: uploadedAttachments,
						params,
					},
					createdAt: new Date().toISOString(),
					response: [
						{
							images: generatedImageAttachments,
							createdAt: new Date().toISOString(),
							modelUsed: {
								modelName: getImageModelEndpoint(imageModel, !!uploadedImageUrl),
								modelProvider,
							},
						},
					],
				};

				try {
					const { error } = await ChatMessageDB.createChatMessage(supabase, newMessage);
					if (error) {
						console.error('Failed to save chat message:', error);
						throw new Error('Failed to save chat message');
					}
				} catch (error) {
					console.error('Failed to save chat message:', error);
				}
				break;

			case 'edit':
				try {
					const { error: deleteError } = await ChatMessageDB.deleteChatMessage(
						supabase,
						messageId
					);
					if (deleteError) {
						console.error('Failed to delete chat message:', deleteError);
						throw new Error('Failed to delete chat message');
					}
				} catch (error) {
					console.error('Failed to delete chat message:', error);
				}

				const editedMessage: ChatMessage = {
					chatId,
					id: messageId,
					prompt: {
						text: prompt,
						attachments: uploadedAttachments,
						params,
					},
					createdAt: new Date().toISOString(),
					response: [
						{
							images: generatedImageAttachments,
							createdAt: new Date().toISOString(),
							modelUsed: {
								modelName: getImageModelEndpoint(imageModel, !!uploadedImageUrl),
								modelProvider,
							},
						},
					],
				};

				try {
					const { error } = await ChatMessageDB.createChatMessage(
						supabase,
						editedMessage
					);
					if (error) {
						console.error('Failed to save edited chat message:', error);
						throw new Error('Failed to save edited chat message');
					}
				} catch (error) {
					console.error('Failed to save edited chat message:', error);
				}
				break;

			case 'regenerate':
				const newResponse = {
					images: generatedImageAttachments,
					createdAt: new Date().toISOString(),
					modelUsed: {
						modelName: getImageModelEndpoint(imageModel, !!uploadedImageUrl),
						modelProvider,
					},
				};

				try {
					const { error } = await ChatMessageDB.appendChatMessageResponse(
						supabase,
						messageId,
						newResponse
					);
					if (error) {
						console.error('Failed to append chat message response:', error);
						throw new Error('Failed to append chat message response');
					}
				} catch (error) {
					console.error('Failed to regenerate response:', error);
				}
				break;
		}

		try {
			const { error } = await ChatHistoryDB.updateChatHistoryLastModifiedAt(supabase, chatId);
			if (error) {
				console.error('Failed to update chat history last_modified_at:', error);
				throw new Error('Failed to update chat history last_modified_at');
			}
		} catch (error) {
			console.error('Failed to update chat history last_modified_at:', error);
		}
	});

	return NextResponse.json({ generatedImageResults, uploadedImageUrls }, { status: 200 });
}
