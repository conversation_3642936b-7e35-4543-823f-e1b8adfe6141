import { useRef } from 'react';
import { motion } from 'motion/react';
import { <PERSON>, CardHeader, CardBody } from '@heroui/card';
import { Button } from '@heroui/button';
import Image from 'next/image';
import Link from 'next/link';
import {
	IconNews,
	IconExternalLink,
	IconChevronsLeft,
	IconChevronsRight,
} from '@tabler/icons-react';
import { LocalNews as LocalNewsType } from '@/types/web-search-results';

interface OnlineNewsFeedProps {
	results: LocalNewsType[];
}

export const OnlineNewsFeed: React.FC<OnlineNewsFeedProps> = ({ results }) => {
	const scrollContainerRef = useRef<HTMLDivElement>(null);

	if (!results.length) {
		return null;
	}

	const scroll = (direction: 'left' | 'right') => {
		const container = scrollContainerRef.current;
		if (!container) return;

		const scrollAmount = container.clientWidth * 0.8;
		container.scrollBy({
			left: direction === 'left' ? -scrollAmount : scrollAmount,
			behavior: 'smooth',
		});
	};

	return (
		<motion.div
			initial={{ opacity: 0, y: 20 }}
			animate={{ opacity: 1, y: 0 }}
			transition={{ duration: 0.3 }}
			className="w-full"
		>
			<Card
				disableRipple
				radius="lg"
				classNames={{
					base: 'w-full bg-transparent border-none shadow-none',
					body: 'relative',
				}}
			>
				<CardHeader className="flex items-center gap-2">
					<IconNews
						size={16}
						className="shrink-0 text-zeco-purple/80"
					/>
					<h3 className="text-sm font-medium text-primary-text">News Feed</h3>
				</CardHeader>
				<CardBody>
					<Button
						isIconOnly
						disableRipple
						radius="full"
						variant="flat"
						onPress={() => scroll('left')}
						className="absolute top-1/2 left-0 z-10 -translate-y-1/2 bg-[#101010]"
					>
						<IconChevronsLeft
							size={20}
							className="text-zeco-purple/80"
						/>
					</Button>
					<div
						ref={scrollContainerRef}
						className="scrollbar-hide flex w-full gap-4 overflow-x-auto scroll-smooth px-4"
					>
						{results.map((news, index) => (
							<motion.div
								key={`${news.title}-${index}`}
								initial={{ opacity: 0, y: 20 }}
								animate={{ opacity: 1, y: 0 }}
								transition={{ duration: 0.3, delay: index * 0.1 }}
								className="w-[320px] flex-none"
							>
								<Card
									disableRipple
									isPressable
									as={Link}
									href={news.link}
									target="_blank"
									rel="noopener noreferrer"
									radius="lg"
									classNames={{
										base: 'transition-all duration-300 hover:shadow-2xl hover:translate-y-[-2px] h-full bg-[#101010] hover:bg-sidebar-hover/50',
										body: 'p-0',
									}}
								>
									<CardBody>
										{news.thumbnail && (
											<div className="relative h-40 w-full overflow-hidden">
												<Image
													src={news.thumbnail}
													alt={`News article: ${news.title} from ${news.source}`}
													fill
													sizes="320px"
													quality={85}
													placeholder="blur"
													blurDataURL="data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wBDAAYEBQYFBAYGBQYHBwYIChAKCgkJChQODwwQFxQYGBcUFhYaHSUfGhsjHBYWICwgIyYnKSopGR8tMC0oMCUoKSj/2wBDAQcHBwoIChMKChMoGhYaKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCj/wAARCAAIAAoDASIAAhEBAxEB/8QAFQABAQAAAAAAAAAAAAAAAAAAAAv/xAAhEAACAQMDBQAAAAAAAAAAAAABAgMABAUGIWGRkqGx/8QAFQEBAQAAAAAAAAAAAAAAAAAAAAX/xAAhEQACAQIHAQAAAAAAAAAAAAABAgADBAUREiExQVFhkf/aAAwDAQACEQMRAD8AltJagyeH0AthI5xdrLcNM91BF5pX2HaH9bcfaSXWGaRmknyJckliyjqTzSlT54b6bk+h0R//2Q=="
													priority={index < 2}
													className="object-cover transition-transform duration-500 hover:scale-110"
												/>
											</div>
										)}
										<div className="p-4">
											<div className="flex items-start justify-between gap-2">
												<div className="min-w-0 flex-1">
													<h4 className="line-clamp-2 text-sm font-medium text-primary-text">
														{news.title}
													</h4>
												</div>
												<IconExternalLink
													size={14}
													className="mt-1 shrink-0 text-secondary-text/40 transition-colors duration-200 hover:text-zeco-purple/80"
												/>
											</div>
											<div className="mt-3">
												<div className="flex items-center gap-2 text-xs text-secondary-text/80">
													<span className="text-zeco-purple/80">
														{news.source}
													</span>
													{news.date && (
														<>
															<span className="text-secondary-text/40">
																•
															</span>
															<span>{news.date}</span>
														</>
													)}
												</div>
											</div>
										</div>
									</CardBody>
								</Card>
							</motion.div>
						))}
					</div>
					<Button
						isIconOnly
						disableRipple
						radius="full"
						variant="flat"
						onPress={() => scroll('right')}
						className="absolute top-1/2 right-0 z-10 -translate-y-1/2 bg-[#101010]"
					>
						<IconChevronsRight
							size={20}
							className="text-zeco-purple/80"
						/>
					</Button>
				</CardBody>
			</Card>
		</motion.div>
	);
};
