import { Attachment as SDKAttachment } from 'ai';
import Image from 'next/image';
import { useRef, useState } from 'react';
import { EditIcon } from '@/components/icons';
import { Button } from '@heroui/button';
import { Chip } from '@heroui/chip';
import { Textarea } from '@heroui/input';
import { Tooltip } from '@heroui/tooltip';
import { IconCheck, IconCornerDownRight, IconX } from '@tabler/icons-react';
import { ModelType, useModelContext } from '@/app/(post-auth)/providers';
import { Attachment } from '@/types/chat';

interface UserMessageProps {
	prompt: string;
	attachments?: Attachment[] | SDKAttachment[];
	onEdit: (model: ModelType, prompt: string) => void;
	isEditAvailable: boolean;
}

const getAttachmentMimeType = (file: Attachment | SDKAttachment) =>
	(file as any).type || (file as any).contentType || '';

const UserMessageComponent: React.FC<UserMessageProps> = ({
	prompt,
	attachments,
	onEdit,
	isEditAvailable,
}) => {
	const model = useModelContext();
	const formRef = useRef<HTMLFormElement>(null);
	const [isEditing, setIsEditing] = useState(false);
	const [editedPrompt, setEditedPrompt] = useState(prompt);

	const imageAttachments = (attachments || []).filter((file) =>
		getAttachmentMimeType(file).startsWith('image/')
	);
	const nonImageAttachments = (attachments || []).filter(
		(file) => !getAttachmentMimeType(file).startsWith('image/')
	);

	const handleSubmit = (e: React.FormEvent<HTMLFormElement>) => {
		e.preventDefault();
		if (editedPrompt.trim() !== prompt.trim()) {
			onEdit(model, editedPrompt.trim());
		}
		setIsEditing(false);
	};

	const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
		if (e.key === 'Enter' && !e.shiftKey) {
			e.preventDefault();
			if (formRef.current?.requestSubmit) {
				formRef.current.requestSubmit();
			} else {
				// Fallback for older browsers
				formRef.current?.dispatchEvent(
					new Event('submit', { cancelable: true, bubbles: true })
				);
			}
		}
	};

	const handleCancel = () => {
		setEditedPrompt(prompt);
		setIsEditing(false);
	};

	return (
		<div className="mb-6 flex flex-col space-y-2">
			<div className="group flex items-center justify-end gap-2">
				{!isEditing && isEditAvailable && (
					<Tooltip
						content="Edit message"
						placement="bottom"
						size="sm"
						radius="sm"
						className="bg-gray-50 text-[#090909]"
						delay={500}
						closeDelay={0}
					>
						<Button
							disableRipple
							isIconOnly
							size="sm"
							variant="light"
							radius="full"
							onPress={() => setIsEditing(true)}
							className="self-center opacity-0 transition-opacity group-hover:opacity-100"
						>
							<EditIcon
								size={16}
								className="text-secondary-text hover:text-white"
							/>
						</Button>
					</Tooltip>
				)}
				<div className={`${isEditing ? 'grow' : 'shrink'} max-w-[85%]`}>
					{isEditing ? (
						<form
							ref={formRef}
							onSubmit={handleSubmit}
							className="flex w-full flex-col space-y-4 rounded-3xl bg-[#101010] px-6 py-4"
						>
							<Textarea
								value={editedPrompt}
								onChange={(e) => setEditedPrompt(e.target.value)}
								onKeyDown={handleKeyDown}
								variant="flat"
								minRows={2}
								maxRows={4}
								classNames={{
									base: 'w-full bg-[#101010]',
									input: 'text-white/90 placeholder:text-white/60',
									inputWrapper:
										'bg-[#101010] data-[focus=true]:bg-[#101010] data-[hover=true]:bg-[#101010] group-data-[focus=true]:bg-[#101010]',
								}}
							/>
							<div className="flex flex-row justify-end gap-2">
								<Button
									disableRipple
									size="sm"
									variant="light"
									radius="full"
									onPress={handleCancel}
									startContent={<IconX size={16} />}
									className="border-neutral-200 text-neutral-500 dark:border-neutral-700 dark:text-neutral-400"
								>
									Cancel
								</Button>
								<Button
									type="submit"
									disableRipple
									size="sm"
									variant="light"
									radius="full"
									isDisabled={
										!editedPrompt.trim() ||
										editedPrompt.trim() === prompt.trim()
									}
									startContent={<IconCheck size={16} />}
									className="border-blue-200 bg-blue-50 text-blue-500 dark:border-blue-900 dark:bg-blue-950 dark:text-blue-400"
								>
									Save
								</Button>
							</div>
						</form>
					) : (
						<div className="rounded-2xl bg-[#101010] px-4 py-2.5 break-words text-primary-text backdrop-blur-sm">
							{prompt}
							{nonImageAttachments.length > 0 && (
								<div className="mt-2 flex flex-wrap gap-2">
									{nonImageAttachments.map((file, index) => (
										<Chip
											key={index}
											size="sm"
											variant="dot"
										>
											{file.name || 'Attached file'}
										</Chip>
									))}
								</div>
							)}
						</div>
					)}
				</div>
			</div>

			{imageAttachments.length > 0 && (
				<div className="flex w-full flex-wrap items-center justify-end gap-2">
					<IconCornerDownRight
						size={16}
						className="text-secondary-text"
					/>
					{imageAttachments.map((file, index) => (
						<div
							key={index}
							className="relative h-12 w-12 overflow-hidden rounded-lg border border-white/10"
						>
							<Image
								src={file.url}
								alt={file.name || 'Attached image'}
								fill
								className="rounded-lg object-cover"
								sizes="48px"
							/>
						</div>
					))}
				</div>
			)}
		</div>
	);
};

export default UserMessageComponent;
